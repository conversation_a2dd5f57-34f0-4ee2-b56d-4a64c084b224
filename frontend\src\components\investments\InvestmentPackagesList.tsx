import React, { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON>ge,
  Button,
  HStack,
  VStack,
  Text,
  Progress,
  Tooltip,
  useToast,
  Alert,
  AlertIcon,
  Spinner,
  Center,
  Select,
  Input,
  InputGroup,
  InputLeftElement,
  Flex,
  Spacer
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { FaSearch, FaEye, FaCoins, FaClock, FaChartLine } from 'react-icons/fa';

const MotionTr = motion(Tr);

interface InvestmentPackage {
  _id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  createdAt: string;
  activatedAt: string;
  totalEarned: number;
  dailyInterest: number;
  interestRate: number;
  activeDays: number;
  compoundEnabled: boolean;
  currentDailyEarnings: number;
  projectedValue: number;
}

interface InvestmentPackagesListProps {
  onRefresh: () => void;
}

const InvestmentPackagesList: React.FC<InvestmentPackagesListProps> = ({ onRefresh }) => {
  const [packages, setPackages] = useState<InvestmentPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [total, setTotal] = useState(0);
  const [statusFilter, setStatusFilter] = useState('');
  const [currencyFilter, setCurrencyFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  
  const toast = useToast();

  const fetchPackages = async () => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(statusFilter && { status: statusFilter }),
        ...(currencyFilter && { currency: currencyFilter })
      });

      const response = await fetch(`/api/investment-packages/packages?${params}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setPackages(data.data.packages);
        setTotal(data.data.pagination.total);
      } else {
        // Mock data for development
        const mockPackages: InvestmentPackage[] = [
          {
            _id: '1',
            amount: 1000,
            currency: 'USDT',
            status: 'active',
            createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            activatedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
            totalEarned: 40,
            dailyInterest: 10,
            interestRate: 0.01,
            activeDays: 4,
            compoundEnabled: false,
            currentDailyEarnings: 10,
            projectedValue: 1040
          },
          {
            _id: '2',
            amount: 2500,
            currency: 'USDT',
            status: 'active',
            createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
            activatedAt: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000).toISOString(),
            totalEarned: 225,
            dailyInterest: 25,
            interestRate: 0.01,
            activeDays: 9,
            compoundEnabled: true,
            currentDailyEarnings: 25,
            projectedValue: 2725
          },
          {
            _id: '3',
            amount: 500,
            currency: 'BTC',
            status: 'pending',
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            activatedAt: new Date(Date.now() + 5 * 60 * 60 * 1000).toISOString(),
            totalEarned: 0,
            dailyInterest: 0,
            interestRate: 0.01,
            activeDays: 0,
            compoundEnabled: false,
            currentDailyEarnings: 0,
            projectedValue: 500
          }
        ];
        
        setPackages(mockPackages);
        setTotal(mockPackages.length);
      }
    } catch (error) {
      console.error('Error fetching packages:', error);
      toast({
        title: 'Hata',
        description: 'Yatırım paketleri yüklenirken hata oluştu',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackages();
  }, [page, statusFilter, currencyFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'pending': return 'yellow';
      case 'completed': return 'blue';
      case 'withdrawn': return 'gray';
      default: return 'gray';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'pending': return 'Beklemede';
      case 'completed': return 'Tamamlandı';
      case 'withdrawn': return 'Çekildi';
      default: return status;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR', {
      timeZone: 'Europe/Istanbul',
      dateStyle: 'short',
      timeStyle: 'short'
    });
  };

  const calculateROI = (totalEarned: number, amount: number) => {
    return ((totalEarned / amount) * 100).toFixed(2);
  };

  const filteredPackages = packages.filter(pkg => {
    if (searchTerm) {
      return pkg.currency.toLowerCase().includes(searchTerm.toLowerCase()) ||
             pkg._id.toLowerCase().includes(searchTerm.toLowerCase());
    }
    return true;
  });

  if (loading) {
    return (
      <Center py={8}>
        <VStack spacing={4}>
          <Spinner size="lg" color="gold.400" />
          <Text color="gray.400">Yatırım paketleri yükleniyor...</Text>
        </VStack>
      </Center>
    );
  }

  return (
    <VStack spacing={6} align="stretch">
      {/* Filters */}
      <Flex gap={4} wrap="wrap">
        <InputGroup maxW="300px">
          <InputLeftElement pointerEvents="none">
            <FaSearch color="#A0AEC0" />
          </InputLeftElement>
          <Input
            placeholder="Ara (Para birimi, ID)"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            bg="gray.700"
            borderColor="gray.600"
            color="white"
            _placeholder={{ color: 'gray.400' }}
            _focus={{ borderColor: 'gold.400' }}
          />
        </InputGroup>

        <Select
          placeholder="Tüm Durumlar"
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          maxW="200px"
          bg="gray.700"
          borderColor="gray.600"
          color="white"
          _focus={{ borderColor: 'gold.400' }}
        >
          <option value="active" style={{ backgroundColor: '#2D3748' }}>Aktif</option>
          <option value="pending" style={{ backgroundColor: '#2D3748' }}>Beklemede</option>
          <option value="completed" style={{ backgroundColor: '#2D3748' }}>Tamamlandı</option>
          <option value="withdrawn" style={{ backgroundColor: '#2D3748' }}>Çekildi</option>
        </Select>

        <Select
          placeholder="Tüm Para Birimleri"
          value={currencyFilter}
          onChange={(e) => setCurrencyFilter(e.target.value)}
          maxW="200px"
          bg="gray.700"
          borderColor="gray.600"
          color="white"
          _focus={{ borderColor: 'gold.400' }}
        >
          <option value="USDT" style={{ backgroundColor: '#2D3748' }}>USDT</option>
          <option value="BTC" style={{ backgroundColor: '#2D3748' }}>BTC</option>
          <option value="ETH" style={{ backgroundColor: '#2D3748' }}>ETH</option>
          <option value="BNB" style={{ backgroundColor: '#2D3748' }}>BNB</option>
        </Select>

        <Spacer />

        <Button
          leftIcon={<FaChartLine />}
          colorScheme="blue"
          variant="outline"
          onClick={() => {
            onRefresh();
            fetchPackages();
          }}
        >
          Yenile
        </Button>
      </Flex>

      {/* Packages Table */}
      {filteredPackages.length === 0 ? (
        <Alert status="info" bg="blue.900" borderColor="blue.400" borderWidth="1px">
          <AlertIcon color="blue.400" />
          <Text color="blue.200">
            {packages.length === 0 
              ? "Henüz yatırım paketiniz bulunmuyor. İlk yatırımınızı oluşturun!"
              : "Arama kriterlerinize uygun paket bulunamadı."
            }
          </Text>
        </Alert>
      ) : (
        <Box overflowX="auto">
          <Table variant="simple" size="md">
            <Thead>
              <Tr>
                <Th color="gray.400" borderColor="gray.600">Paket ID</Th>
                <Th color="gray.400" borderColor="gray.600">Miktar</Th>
                <Th color="gray.400" borderColor="gray.600">Para Birimi</Th>
                <Th color="gray.400" borderColor="gray.600">Durum</Th>
                <Th color="gray.400" borderColor="gray.600">Toplam Kazanç</Th>
                <Th color="gray.400" borderColor="gray.600">Günlük Kazanç</Th>
                <Th color="gray.400" borderColor="gray.600">ROI</Th>
                <Th color="gray.400" borderColor="gray.600">Aktif Gün</Th>
                <Th color="gray.400" borderColor="gray.600">Oluşturulma</Th>
                <Th color="gray.400" borderColor="gray.600">İşlemler</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredPackages.map((pkg, index) => (
                <MotionTr
                  key={pkg._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  _hover={{ bg: 'gray.700' }}
                >
                  <Td borderColor="gray.600" color="gray.300" fontSize="sm">
                    <Tooltip label={pkg._id}>
                      <Text isTruncated maxW="100px">
                        {pkg._id}
                      </Text>
                    </Tooltip>
                  </Td>
                  <Td borderColor="gray.600" color="white" fontWeight="bold">
                    {pkg.amount.toFixed(6)}
                  </Td>
                  <Td borderColor="gray.600">
                    <HStack spacing={2}>
                      <FaCoins color="#FCD535" />
                      <Text color="gold.400" fontWeight="bold">{pkg.currency}</Text>
                    </HStack>
                  </Td>
                  <Td borderColor="gray.600">
                    <Badge colorScheme={getStatusColor(pkg.status)} variant="solid">
                      {getStatusText(pkg.status)}
                    </Badge>
                  </Td>
                  <Td borderColor="gray.600" color="green.400" fontWeight="bold">
                    {pkg.totalEarned.toFixed(6)} {pkg.currency}
                  </Td>
                  <Td borderColor="gray.600" color="purple.400" fontWeight="bold">
                    {pkg.currentDailyEarnings.toFixed(6)} {pkg.currency}
                  </Td>
                  <Td borderColor="gray.600">
                    <Text color={parseFloat(calculateROI(pkg.totalEarned, pkg.amount)) > 0 ? "green.400" : "gray.400"}>
                      %{calculateROI(pkg.totalEarned, pkg.amount)}
                    </Text>
                  </Td>
                  <Td borderColor="gray.600" color="blue.400">
                    {pkg.activeDays} gün
                  </Td>
                  <Td borderColor="gray.600" color="gray.400" fontSize="sm">
                    {formatDate(pkg.createdAt)}
                  </Td>
                  <Td borderColor="gray.600">
                    <Button
                      size="sm"
                      leftIcon={<FaEye />}
                      colorScheme="blue"
                      variant="outline"
                      onClick={() => {
                        // TODO: Open package details modal
                        toast({
                          title: 'Paket Detayları',
                          description: `${pkg._id} paket detayları yakında eklenecek`,
                          status: 'info',
                          duration: 3000,
                          isClosable: true,
                        });
                      }}
                    >
                      Detay
                    </Button>
                  </Td>
                </MotionTr>
              ))}
            </Tbody>
          </Table>
        </Box>
      )}

      {/* Pagination */}
      {total > limit && (
        <HStack justify="center" spacing={4}>
          <Button
            isDisabled={page === 1}
            onClick={() => setPage(page - 1)}
            variant="outline"
            colorScheme="gray"
          >
            Önceki
          </Button>
          <Text color="gray.400">
            Sayfa {page} / {Math.ceil(total / limit)}
          </Text>
          <Button
            isDisabled={page >= Math.ceil(total / limit)}
            onClick={() => setPage(page + 1)}
            variant="outline"
            colorScheme="gray"
          >
            Sonraki
          </Button>
        </HStack>
      )}
    </VStack>
  );
};

export default InvestmentPackagesList;
